<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import type { DispatchContactCardEmit } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { computed, ref, useTemplateRef, watch } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import { useCommonContact } from '@/utils/callContact/commonContact'
  import openDialog from '@/utils/dialog'
  import CommonContactEdit from '../dialog/CommonContactEdit.vue'
  import { setSpeakTarget, globalVoipServerManager, speakState } from '@/utils/speak'
  import BfSpeaking from '@/platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue'
  import type { Contact, ContactTerminalType, ContactGroupType } from '@/utils/callContact/types'

  const { t } = useI18n()
  const { commonContacts, moveCommonContact } = useCommonContact()

  const emit = defineEmits<DispatchContactCardEmit>()

  // 判断是否为单呼联系人
  const isSingleCallContact = (contact: Contact): boolean => {
    const singleCallTypes: ContactTerminalType[] = ['sdcTerminal', 'networkTerminal']
    return singleCallTypes.includes(contact.type as ContactTerminalType)
  }

  // 判断是否为组呼联系人
  const isGroupCallContact = (contact: Contact): boolean => {
    const groupCallTypes: (ContactGroupType | 'fullCallContact')[] = ['group', 'taskGroup', 'tempGroup', 'fullCallContact']
    return groupCallTypes.includes(contact.type as ContactGroupType | 'fullCallContact')
  }
  const scrollerRef = useTemplateRef('scroller')

  watch(
    () => commonContacts.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(132)
    itemSecondarySize.value = calcScaleSize(228)
  })

  const currentContactRids = computed<string[]>(() => {
    const currentContactRids: string[] = []
    commonContacts.value.forEach(item => {
      currentContactRids.push(item.targetRid)
    })
    return currentContactRids
  })

  const openCommonContactsDialog = () => {
    openDialog(CommonContactEdit, {
      currentContactRids: currentContactRids.value,
    })
  }

  // 呼叫函数
  const call = (targetDmrId: string, item: Contact, callback?: (success: boolean) => void) => {
    // 如果正在呼叫，则直接返回
    if (speakState.current === 1) {
      callback?.(false)
      return
    }

    // 检查是否已经打开了 BfSpeaking 对话框
    if (bfglob.vspeaking && bfglob.vspeaking.visible) {
      // 如果已经打开，直接设置目标并快速呼叫
      setSpeakTarget(item.targetRid)
      bfglob.vspeaking
        .speakFast(targetDmrId)
        .then(() => {
          // 只有在通话真正连通后才执行 callback(true)
          callback?.(true)
        })
        .catch(() => {
          callback?.(false)
        })
    } else {
      // 如果没有打开，则打开对话框
      openDialog(BfSpeaking).then(vm => {
        setSpeakTarget(item.targetRid)
        ;(vm.value as InstanceType<typeof BfSpeaking>)
          .speakFast(targetDmrId)
          .then(() => {
            // 只有在通话真正连通后才执行 callback(true)
            callback?.(true)
          })
          .catch(() => {
            callback?.(false)
          })
      })
    }
  }

  // 挂断函数
  const hangup = (targetDmrId: string, item: Contact, callback?: (success: boolean) => void) => {
    if (speakState.current === 1) {
      globalVoipServerManager.sl_i_speak_end()
    }
    callback?.(true)
  }

  // 定位函数
  const locate = (targetDmrId: string, item: Contact) => {
    // 打开定位监控对话框
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: item.targetRid, cmdType: 'cb01' })
    })
  }

  // 发送指令函数
  const sendCommand = async (targetDmrId: string, item: Contact) => {
    // 打开发送指令对话框
    const SendCmd = await import('@/platform/dispatch/CommunicationDispatch/dialog/sendCmd.vue')
    type SendCmdComponent = InstanceType<typeof SendCmd.default>
    openDialog(SendCmd.default).then(vm => {
      // 等待组件挂载完成后设置目标
      setTimeout(() => {
        const sendCmdInstance = vm.value as SendCmdComponent
        if (sendCmdInstance && sendCmdInstance.cmdTarget) {
          // 判断是组呼还是单呼
          if (isSingleCallContact(item)) {
            // 单呼：添加到 device 数组
            sendCmdInstance.cmdTarget.device = [targetDmrId]
            sendCmdInstance.cmdTarget.groud = []
          } else if (isGroupCallContact(item)) {
            // 组呼：添加到 groud 数组
            sendCmdInstance.cmdTarget.groud = [targetDmrId]
            sendCmdInstance.cmdTarget.device = []
          }

          // 滚动到指定行
          const dialogTableTreeRef = sendCmdInstance.$refs?.dialogTableTree
          if (dialogTableTreeRef?.tableTreeRef) {
            dialogTableTreeRef?.tableTreeRef.scrollToRowByRid(item.targetRid)
          }
        }
      }, 100)
    })
  }

  // 发送文本短信函数
  const sendTextMessage = async (targetDmrId: string, item: Contact) => {
    // 打开快速发送命令对话框，指定为文本短信类型
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: item.targetRid, cmdType: 'cb31' })
    })
  }

  // 拖拽相关状态
  const dragState = ref({
    isDragging: false,
    dragIndex: -1,
    dropIndex: -1,
  })

  // 拖拽开始
  const handleDragStart = (index: number, event: DragEvent) => {
    dragState.value.isDragging = true
    dragState.value.dragIndex = index

    // 设置拖拽数据
    if (event.dataTransfer) {
      event.dataTransfer.effectAllowed = 'move'
      event.dataTransfer.setData('text/plain', index.toString())

      // 创建拖拽时的预览图像（可选）
      const target = event.target as HTMLElement
      const card = target.closest('.card-container') as HTMLElement
      if (card) {
        // 克隆卡片作为拖拽预览
        const dragImage = card.cloneNode(true) as HTMLElement
        dragImage.style.opacity = '0.8'
        document.body.appendChild(dragImage)
        event.dataTransfer.setDragImage(dragImage, card.offsetWidth / 2, card.offsetHeight / 2)

        // 延迟移除克隆的元素
        setTimeout(() => {
          document.body.removeChild(dragImage)
        }, 0)
      }
    }
  }

  // 拖拽结束
  const handleDragEnd = () => {
    if (
      dragState.value.isDragging &&
      dragState.value.dragIndex !== -1 &&
      dragState.value.dropIndex !== -1 &&
      dragState.value.dragIndex !== dragState.value.dropIndex
    ) {
      moveCommonContact(dragState.value.dragIndex, dragState.value.dropIndex)

      // 保存当前 commonContacts 的 rid 顺序到 localStorage
      try {
        const ridOrder = commonContacts.value.map(contact => contact.rid)
        localStorage.setItem('bf8100-common-contacts-order', JSON.stringify(ridOrder))
      } catch (error) {
        console.warn('Failed to save contacts order to localStorage:', error)
      }
    }

    // 重置状态
    dragState.value.isDragging = false
    dragState.value.dragIndex = -1
    dragState.value.dropIndex = -1
  }

  // 拖拽悬停
  const handleDragOver = (index: number) => {
    if (dragState.value.isDragging && dragState.value.dragIndex !== index) {
      dragState.value.dropIndex = index
    }
  }
</script>

<template>
  <PageHeader :title="t('dispatch.commonContacts')">
    <DispatchTitleIcon icon="bfdx-bianjiyangshi2neibu" @click="openCommonContactsDialog" />
  </PageHeader>
  <RecycleScroller
    ref="scroller"
    class="contact-container"
    :items="commonContacts"
    :item-size="itemSize"
    :grid-items="5"
    :item-secondary-size="itemSecondarySize"
    key-field="rid"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        :key="index"
        v-bind="item"
        :class="{
          dragging: dragState.isDragging && dragState.dragIndex === index,
          'drop-target': dragState.isDragging && dragState.dropIndex === index && dragState.dragIndex !== index,
        }"
        @locate="targetDmrId => locate(targetDmrId, item)"
        @call="(targetDmrId, callback) => call(targetDmrId, item, callback)"
        @hangup="(targetDmrId, callback) => hangup(targetDmrId, item, callback)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => sendCommand(targetDmrId, item)"
        @send-message="targetDmrId => sendTextMessage(targetDmrId, item)"
        @dragover.prevent="handleDragOver(index)"
        @drop.prevent="handleDragEnd"
      >
        <template #drag>
          <div
            class="drag-box h-[10px] absolute top-0 left-0 right-0 cursor-move z-20"
            draggable="true"
            @dragstart="event => handleDragStart(index, event)"
            @dragend="handleDragEnd"
          ></div>
        </template>
      </DispatchContactCard>
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));

    :deep(.vue-recycle-scroller__item-wrapper) {
      overflow: visible;
    }

    .card-container {
      margin: 25px;
    }
  }

  // 拖拽状态样式
  :deep(.card-container) {
    transition: all 0.2s ease;

    &.dragging {
      opacity: 0.5;
      transform: scale(0.95);
      z-index: 1000;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    }

    &.drop-target {
      border: 2px dashed #00bfff;
      background: rgba(0, 191, 255, 0.1);
      transform: scale(1.02);
    }
  }
</style>
