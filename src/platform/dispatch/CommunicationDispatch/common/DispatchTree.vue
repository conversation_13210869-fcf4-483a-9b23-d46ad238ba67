<template>
  <div class="dispatch-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="true"
      :filter="true"
      :enable-checkbox="props.enableCheckbox"
      :menuConfig="menuConfig"
      @menu-click="menuEventHandler"
      @cell-dblclick.prevent="cellDbClickHandler"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, useTemplateRef } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { DeviceTypes, getDeviceMapLonLat } from '@/utils/bfutil'
  import { MenuConfig, MenuEventHandler, TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import openDialog from '@/utils/dialog'
  import BfSpeaking from '@/platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue'
  import { VxeTableEvents } from 'vxe-table'
  import { mapFlyTo } from '@/utils/map'
  import { setSpeakTarget } from '@/utils/speak'

  import { useCommonContact } from '@/utils/callContact/commonContact'
  import {
    deleteDbGroupCallContactByOrgId2db,
    deleteDbSingleCallContactByDeviceId2db,
    insertDbGroupCallContact2db,
    insertDbSingleCallContact2db,
  } from '@/utils/callContact'
  import bfNotify from '@/utils/notify'

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]

  const props = withDefaults(
    defineProps<{
      enableCheckbox?: boolean
    }>(),
    {
      enableCheckbox: true,
    }
  )

  const statusContextMenuCode = ['stats', 'cb01', 'cb02', 'cb09']

  const { t } = useI18n()

  const tableTreeRef = useTemplateRef('tableTree')

  const { commonContacts } = useCommonContact()

  const menuConfig = computed<MenuConfig>(() => {
    return {
      body: {
        options: [
          [
            {
              name: t('tree.quickCall'),
              code: 'quickCall',
            },
            {
              disabled: false,
              visible: true,
              name: t('tree.addToCommonContact'),
              code: 'addToCommonContact',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.removeFromCommonContact'),
              code: 'removeFromCommonContact',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.locateCtrl'),
              code: 'cb01',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.trailCtrl'),
              code: 'cb02',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.telecontrol'),
              code: 'cb09',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.status'),
              code: 'stats',
            },
          ],
          [
            {
              name: t('tree.collapseAll'),
              code: 'collapseAll',
            },
            {
              name: t('tree.expandAll'),
              code: 'expandAll',
            },
            {
              name: t('tree.online'),
              code: 'displayOnline',
            },
            {
              name: t('tree.displayAllDev'),
              code: 'displayAll',
            },
          ],
        ],
      },
      visibleMethod: ({ options, row }) => {
        if (!row) return true
        let hasStatus = false

        if (row.nodeType === TreeNodeType.Terminal) {
          const device = bfglob.gdevices.get(row.rid)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
        }

        const visible = row.nodeType === TreeNodeType.Org ? false : hasStatus
        const disabled = !visible

        options?.forEach(list => {
          list.forEach(item => {
            if (statusContextMenuCode.includes(item.code)) {
              item.visible = visible
              item.disabled = disabled
            }

            if (item.code === 'addToCommonContact') {
              const isInCommonContact = commonContacts.value.some(contact => contact.targetRid === row.rid)
              item.visible = !isInCommonContact
              item.disabled = isInCommonContact
            }

            if (item.code === 'removeFromCommonContact') {
              const isInCommonContact = commonContacts.value.some(contact => contact.targetRid === row.rid)
              item.visible = isInCommonContact
              item.disabled = !isInCommonContact
            }
          })
        })

        return true
      },
    } satisfies MenuConfig
  })

  const menuEventHandler: MenuEventHandler = ({ menu, row }) => {
    // todo: handle click
    switch (menu.code) {
      case 'quickCall':
        // 检查是否已经打开了 BfSpeaking 对话框
        if (bfglob.vspeaking && bfglob.vspeaking.visible) {
          // 如果已经打开，直接设置目标并快速呼叫
          let dmrId = ''
          if (row.nodeType === TreeNodeType.Terminal) {
            const data = bfglob.gdevices.get(row.rid)
            dmrId = data?.dmrId ?? ''
          } else {
            const data = bfglob.gorgData.get(row.rid)
            dmrId = data?.dmrId ?? ''
          }
          setSpeakTarget(row.rid)
          bfglob.vspeaking.speakFast(dmrId)
        } else {
          // 如果没有打开，则打开对话框
          let dmrId = ''
          if (row.nodeType === TreeNodeType.Terminal) {
            const data = bfglob.gdevices.get(row.rid)
            dmrId = data?.dmrId ?? ''
          } else {
            const data = bfglob.gorgData.get(row.rid)
            dmrId = data?.dmrId ?? ''
          }
          openDialog(BfSpeaking).then(vm => {
            setSpeakTarget(row.rid)
            ;(vm.value as InstanceType<typeof BfSpeaking>).speakFast(dmrId)
          })
        }
        break
      case 'addToCommonContact':
        handleAddToCommonContact(row)
        break
      case 'removeFromCommonContact':
        handleRemoveFromCommonContact(row)
        break
      case 'cb01':
        // 处理 cb01
        break
      case 'cb02':
        // 处理 cb02
        break
      case 'cb09':
        // 处理 cb09
        break
      case 'stats':
        // 处理状态
        break
      case 'collapseAll':
        tableTreeRef.value?.collapseAll()
        tableTreeRef.value
        break
      case 'expandAll':
        tableTreeRef.value?.expandAll()
        break
      case 'displayOnline':
        tableTreeRef.value?.displayOnline()
        break
      case 'displayAll':
        tableTreeRef.value?.displayAll()
        break
    }
  }

  function handleAddToCommonContact(row: TreeNodeData) {
    if (row.nodeType === TreeNodeType.Org) {
      insertDbGroupCallContact2db(row.rid, 0)
        .then(() => {
          bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.addError'), 'error')
        })
    } else if (row.nodeType === TreeNodeType.Terminal) {
      insertDbSingleCallContact2db(row.rid, 0)
        .then(() => {
          bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.addError'), 'error')
        })
    }
  }

  function handleRemoveFromCommonContact(row: TreeNodeData) {
    if (row.nodeType === TreeNodeType.Org) {
      deleteDbGroupCallContactByOrgId2db(row.rid)
        .then(() => {
          bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.delError'), 'error')
        })
    } else if (row.nodeType === TreeNodeType.Terminal) {
      deleteDbSingleCallContactByDeviceId2db(row.rid)
        .then(() => {
          bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.delError'), 'error')
        })
    }
  }

  const cellDbClickHandler: VxeTableEvents.CellDblclick<TreeNodeData> = ({ row }) => {
    // 处理单元格双击事件
    if (row.nodeType === TreeNodeType.Org) {
      const org = bfglob.gorgData.get(row.rid)
      if (!org) {
        return
      }
      // use relative org marker lonlat
      const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(org.rid)
      if (!lonlatInfo.lonlat) {
        return
      }
      mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
    } else {
      const device = bfglob.gdevices.get(row.rid)
      if (!device) {
        return
      }
      mapFlyTo(getDeviceMapLonLat(device))
    }
  }
</script>

<style lang="scss">
  .dispatch-tree-wrapper {
    height: 100%;
    width: 100%;
  }
</style>
