<template>
  <BfDialog
    v-model="pocVisible"
    :title="pocDialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :fullscreen="fullscreen"
    append-to-body
    class="poc-settings-dialog"
  >
    <el-form
      ref="pocForm"
      :label-width="pocManagerLabelWidth"
      :model="pocSettingData"
      :rules="rules"
      label-position="top"
      :class="['flex', 'gap-3', 'poc-page-form', 'px-2', ...(isMobile ? ['flex-col-reverse', 'h-100'] : [])]"
    >
      <div :class="isMobile ? 'w-full' : 'poc-form-container'">
        <el-row :gutter="20" :class="isMobile ? 'mr-0' : 'mb-2'" align="middle">
          <el-col :xs="24" :sm="12">
            <el-form-item class="mb-3 send-group form-item-ellipsis" prop="txGroupDmrid">
              <template #label>
                <span class="form-item-label">{{ $t('dialog.sendGroup') }}</span>
              </template>
              <BfSelect
                v-model="pocSettingData.txGroupDmrid"
                :placeholder="$t('dialog.select')"
                filterable
                clearable
                @change="txGroupDmridChange"
                style="height: 50px"
              >
                <el-option v-for="item in txGroupsData" :key="item.key" :label="item.label" :value="item.key" />
              </BfSelect>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item :label="$t('loginDlg.password')" prop="password" class="password mb-3">
              <BfInput v-model="pocSettingData.password" type="password" :maxlength="16" clearable show-password style="height: 50px" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12">
            <el-form-item prop="pocConfig.canEditSubscriptionLocal">
              <BfCheckbox v-model="pocSettingData.pocConfig.canEditSubscriptionLocal" :true-value="1" :false-value="0">
                <span v-text="$t('dialog.localEditRxGroup')" />
              </BfCheckbox>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :class="['form-item-transfer', isMobile ? 'mobile-transfer' : '']">
          <template #label>
            <span class="form-item-label">{{ $t('dialog.rxGroupsSetting') }}</span>
          </template>
          <bf-transfer
            v-model="pocSettingData.rxGroupDmrids"
            class="flex md:flex-row flex-col gap-3 justify-between items-center"
            :titles="transferTitles"
            :format="transferLabelFormat"
            :data="availableRxGroup"
            target-order="push"
            @change="checkChange"
          />
        </el-form-item>
      </div>
      <div :class="['overflow-auto', 'flex', 'flex-col', 'flex-grow', 'form-item-tree', ...(isMobile ? ['h-52', 'w-full'] : [])]">
        <div class="flex justify-between pb-2">
          <span :class="isMobile ? 'ml-0' : 'poc-tree-title'">{{ $t('dialog.pocTreeTitle') }}</span>
          <div>
            <span v-text="selectedNodes.length || 0" />
            <span>/</span>
            <span v-text="maxSize" />
          </div>
        </div>
        <!-- <TableTree :ref="treeId" :treeId="treeId" :option="treeOpts" :filterOption="filterOption" @loaded="toDictDefaultTreeNodes" /> -->

        <dialogTableTree
          class="!w-[320px] !h-[calc(500px_-_60px)] m-auto"
          :default-check-keys="contacts"
          @checkbox-change="selectContact"
          @cell-dblclick="handleCellDblClick"
        />
      </div>
    </el-form>
    <template #footer>
      <section class="data_btns flex justify-center gap-3">
        <BfButton color-type="info" @click="pocVisible = false">
          {{ $t('dialog.cancel') }}
        </BfButton>
        <BfButton color-type="warning" @click="confirmEdit()">
          {{ $t('dialog.confirm') }}
        </BfButton>
      </section>
    </template>
  </BfDialog>
</template>

<script>
  import { SupportedLang } from '@/modules/i18n'
  import bfutil, { checkDmrIdIsGroup, getCommonOrgType } from '@/utils/bfutil'
  import dialogTableTree from '@/components/common/dialogTableTree.vue'
  import bfTime from '@/utils/time'
  import bfNotify from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import bfCrypto from '@/utils/crypto'
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import { cloneDeep } from 'lodash'
  import BfDialog from '@/components/bfDialog/main'
  import BfSelect from '@/components/bfSelect/main'
  import BfInput from '@/components/bfInput/main'
  import BfButton from '@/components/bfButton/main'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import BfTransfer from '@/components/bfTransfer/main'
  import { TreeNodeType } from './tableTree'

  const defaultPocSetting = {
    txGroupDmrid: '', // 发射组
    rxGroupDmrids: [], // 接收组
    password: '', // poc终端密码
    pocIndividualContacts: [], // 单呼
    pocGroupContacts: [], // 组呼
    pocConfig: {
      // 0:不可编辑 1:可编辑
      canEditSubscriptionLocal: 0,
    },
  }

  const commonOrgType = getCommonOrgType()
  const filterDynamicGroup = dataList => {
    return dataList.filter(data => commonOrgType.includes(data.orgIsVirtual))
  }
  let selectedTimer = 0
  let passwordCache = ''

  export default {
    name: 'PocSettings',
    emits: ['update:visible', 'syncPocSetting', 'select'],
    components: {
      dialogTableTree,
      BfDialog,
      BfSelect,
      BfInput,
      BfButton,
      BfCheckbox,
      BfTransfer,
    },
    props: {
      visible: {
        type: Boolean,
        required: true,
      },
      device: {
        type: Object,
        required: true,
      },
      isNewStatus: {
        type: Boolean,
        required: true,
      },
    },
    data() {
      return {
        pocSettingData: cloneDeep(defaultPocSetting),
        // 默认发射组数据源
        txGroupsData: [],
        maxSize: 128,
        selectedNodesLen: 0,
      }
    },
    computed: {
      contacts() {
        return [...this.pocSettingData.pocIndividualContacts, ...this.pocSettingData.pocGroupContacts]
      },
      isMobile() {
        return this.$root.layoutLevel === 0
      },
      dbSubject() {
        return `db.${bfglob.sysId}`
      },
      rules() {
        if (this.isNewStatus) {
          return {
            password: [validateRules.required()],
            txGroupDmrid: [validateRules.required()],
          }
        } else {
          return {
            txGroupDmrid: [validateRules.required()],
          }
        }
      },
      pocVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      locale() {
        return this.$i18n.locale
      },
      isEN() {
        return this.locale === SupportedLang.enUS
      },
      isEn() {
        return this.isEN
      },
      isFR() {
        return this.locale === SupportedLang.fr
      },
      deviceName() {
        return this.device.selfId || this.device.dmrId || ''
      },
      pocDialogTitle() {
        const name = this.deviceName
        const title = this.$t('dialog.pocDeviceManage')
        if (!name) return title
        return `${title} - ${name}`
      },
      pocManagerLabelWidth() {
        return this.isFR || this.isEN ? '130px' : '100px'
      },
      pocTableTree() {
        return this.$refs.pocDeviceTree
      },
      selectedNodes() {
        return [...this.pocSettingData.pocGroupContacts, ...this.pocSettingData.pocIndividualContacts]
      },
      transferTitles() {
        return [this.$t('dialog.unListenGroup'), this.$t('dialog.listenGroup')]
      },
      transferLabelFormat() {
        return {
          noChecked: '${total}',

          hasChecked: '${checked}/${total}',
        }
      },
      availableRxGroup() {
        const availableRx = []
        for (let i = 0; i < this.pocSettingData.pocGroupContacts.length; i++) {
          const dmrId = this.pocSettingData.pocGroupContacts[i]
          const org = bfglob.gorgData.getDataByIndex(dmrId)
          if (org) {
            availableRx.push({
              key: org.dmrId,
              label: org.orgShortName,
              disabled: this.pocSettingData.rxGroupDmrids.length === 1 && org.dmrId === this.pocSettingData.rxGroupDmrids[0],
            })
          }
        }
        return availableRx
      },
    },
    methods: {
      selectContact(row, checked) {
        // 传递的是勾选前的值
        if (checked) {
          if (row.nodeType === TreeNodeType.Org) {
            const data = bfglob.gorgData.get(row.rid)
            this.pocSettingData.pocGroupContacts = this.pocSettingData.pocGroupContacts.filter(dmrid => dmrid !== data.dmrId)
          } else {
            const data = bfglob.gdevices.get(row.rid)
            this.pocSettingData.pocIndividualContacts = this.pocSettingData.pocIndividualContacts.filter(dmrid => dmrid !== data.dmrId)
          }
        } else {
          if (row.nodeType === TreeNodeType.Org) {
            const data = bfglob.gorgData.get(row.rid)
            this.pocSettingData.pocGroupContacts.push(data.dmrId)
          } else {
            const data = bfglob.gdevices.get(row.rid)
            this.pocSettingData.pocIndividualContacts.push(data.dmrId)
          }
        }
      },
      // 使用队列方式高效获取节点的所有子孙节点的 dmrId
      getAllDescendantDmrIds(nodeRid) {
        const result = { groupDmrIds: [], individualDmrIds: [] }

        // 获取所有组织数据和设备数据
        const allOrgData = bfglob.gorgData.getAll()
        const allDeviceData = bfglob.gdevices.getAll()

        // 使用队列来遍历，避免深度递归
        const queue = [nodeRid]
        const processedRids = new Set() // 防止循环引用

        while (queue.length > 0) {
          const currentRid = queue.shift()
          if (!currentRid || processedRids.has(currentRid)) {
            continue
          }
          processedRids.add(currentRid)

          // 查找当前节点的子组织
          for (const orgKey in allOrgData) {
            const org = allOrgData[orgKey]
            if (org.parentOrgId === currentRid && !processedRids.has(org.rid)) {
              result.groupDmrIds.push(org.dmrId)
              queue.push(org.rid) // 将子组织加入队列继续处理
            }
          }

          // 查找当前节点的子设备
          for (const deviceKey in allDeviceData) {
            const device = allDeviceData[deviceKey]
            if (device.orgId === currentRid) {
              result.individualDmrIds.push(device.dmrId)
            }
          }
        }

        return result
      },
      // 处理双击事件
      handleCellDblClick(eventData) {
        const { row } = eventData

        // 获取当前节点的数据
        let currentNodeDmrId = ''
        let currentNodeType = row.nodeType

        if (row.nodeType === TreeNodeType.Org) {
          const orgData = bfglob.gorgData.get(row.rid)
          if (orgData) {
            currentNodeDmrId = orgData.dmrId
          }
        } else {
          const deviceData = bfglob.gdevices.get(row.rid)
          if (deviceData) {
            currentNodeDmrId = deviceData.dmrId
          }
        }

        // 获取所有子孙节点的 dmrId
        const descendantDmrIds = this.getAllDescendantDmrIds(row.rid)

        // 构建完整的 dmrId 列表（包含当前节点）
        const allGroupDmrIds =
          currentNodeType === TreeNodeType.Org && currentNodeDmrId ? [currentNodeDmrId, ...descendantDmrIds.groupDmrIds] : descendantDmrIds.groupDmrIds
        const allIndividualDmrIds =
          currentNodeType === TreeNodeType.Terminal && currentNodeDmrId
            ? [currentNodeDmrId, ...descendantDmrIds.individualDmrIds]
            : descendantDmrIds.individualDmrIds

        // 检查是否所有相关的 dmrId 都已经在联系人列表中
        const allGroupsInList = allGroupDmrIds.every(dmrId => this.pocSettingData.pocGroupContacts.includes(dmrId))
        const allIndividualsInList = allIndividualDmrIds.every(dmrId => this.pocSettingData.pocIndividualContacts.includes(dmrId))
        const allInList = allGroupsInList && allIndividualsInList

        if (allInList) {
          // 如果所有节点都在列表中，则移除它们
          this.pocSettingData.pocGroupContacts = this.pocSettingData.pocGroupContacts.filter(dmrId => !allGroupDmrIds.includes(dmrId))
          this.pocSettingData.pocIndividualContacts = this.pocSettingData.pocIndividualContacts.filter(dmrId => !allIndividualDmrIds.includes(dmrId))
        } else {
          // 如果不是所有节点都在列表中，则添加缺失的节点
          allGroupDmrIds.forEach(dmrId => {
            if (!this.pocSettingData.pocGroupContacts.includes(dmrId)) {
              this.pocSettingData.pocGroupContacts.push(dmrId)
            }
          })
          allIndividualDmrIds.forEach(dmrId => {
            if (!this.pocSettingData.pocIndividualContacts.includes(dmrId)) {
              this.pocSettingData.pocIndividualContacts.push(dmrId)
            }
          })
        }
      },
      // 初始化默认发射组的数据源
      initTxGroupsOptions() {
        const list = []
        // 需要过滤掉动态组的数据
        const orgs = filterDynamicGroup(bfutil.objToArray(bfglob.gorgData.getAll()))

        for (const k in orgs) {
          const org = orgs[k]
          list.push({
            key: org.dmrId,
            label: org.orgShortName,
          })
        }
        this.txGroupsData = list
      },
      initPocSettingData() {
        if (!this.device) {
          bfglob.console.warn('initPocSettingData not found device')
          return
        }
        try {
          const pocSettingData = JSON.parse(this.device.pocSetting || '{}')
          this.pocSettingData = {
            ...defaultPocSetting,
            ...pocSettingData,
          }
        } catch (e) {
          bfglob.console.warn('initPocSettingData JSON.parse falid', e, this.device.pocSetting)
        }
        if (!this.isNewStatus) {
          // 编辑状态的时候，缓存密码，未更新密码时使用原密码
          passwordCache = this.pocSettingData.password
          this.pocSettingData.password = ''
          // 判断组呼和单呼的数据都是否存在 不存在则提示用户是否同步
          const address = [...this.pocSettingData.pocGroupContacts, ...this.pocSettingData.pocIndividualContacts]
          const group = []
          const individual = []
          const notInSys = []
          for (let i = 0; i < address.length; i++) {
            const dmrId = address[i]
            const isGroup = checkDmrIdIsGroup(dmrId)
            const target = isGroup ? bfglob.gorgData.getDataByIndex(dmrId) : bfglob.gdevices.getDataByIndex(dmrId)
            if (target) {
              isGroup ? group.push(dmrId) : individual.push(dmrId)
            } else {
              notInSys.push(dmrId)
            }
          }
          if (notInSys.length > 0) {
            this.pocSettingData.pocGroupContacts = group
            this.pocSettingData.pocIndividualContacts = individual
            this.pocSettingData.rxGroupDmrids = this.pocSettingData.rxGroupDmrids.filter(i => !notInSys.includes(i))
            // 默认发射组已被删除? 则使用设备所属组织未作默认组 并添加到联系人 接收组
            if (notInSys.includes(this.pocSettingData.txGroupDmrid)) {
              this.txSetDefaultDeviceOrg()
            }
          }
        }
      },
      txSetDefaultDeviceOrg() {
        if (!this.$refs?.pocDeviceTree?.getRootNode()) {
          setTimeout(() => {
            this.txSetDefaultDeviceOrg()
          }, 50)
          return
        }
        this.pocSettingData.txGroupDmrid = bfglob.gorgData.get(this.device.orgId).dmrId
        this.$refs.pocDeviceTree.setNodeSelected(this.device.orgId, true)
        if (this.pocSettingData.rxGroupDmrids.length === 0) {
          const deviceOrgDmrId = bfglob.gorgData.get(this.device.orgId).dmrId
          deviceOrgDmrId && this.pocSettingData.rxGroupDmrids.push(deviceOrgDmrId)
        }
      },
      initPocSetting() {
        this.initTxGroupsOptions()
        this.initPocSettingData()
      },
      // 深度选中节点，如果当前节点已经选中，则取消当前节点及所有子级节点的选中状态，反之选中节点
      deepSelectedNodes(node, isSelected = false, selectedCount = 0) {
        const children = [node]
        while (children.length > 0 && selectedCount < this.maxSize) {
          const child = children.shift()
          if (!child) {
            continue
          }

          child.data.dblClickSelected = isSelected
          child.setSelected(isSelected)
          if (isSelected) {
            selectedCount++
          } else {
            selectedCount--
          }
          // 执行完当前节点后，判断是否存在子节点，把子节点添加在数组最后，达到遍历该树的所有子孙节点
          if (child.hasChildren()) {
            children.push(...child.getChildren())
          }
        }
      },
      dblclickNode(event, data) {
        const node = data.node
        if (!node) {
          return false
        }

        node.setActive()
        const dblClickSelected = node.data.dblClickSelected
        this.deepSelectedNodes(node, node.unselectable ? !dblClickSelected : !node.selected, this.selectedNodes.length - 1)
        return false
      },
      beforeSelect(event, data) {
        const node = data.node
        if (!node) {
          return true
        }
        if (node.isSelected()) {
          return true
        }
        return !(this.selectedNodesLen >= this.maxSize)
      },
      syncPocSettingToDevice(pocSettingJson, lastModifyTime) {
        this.$emit('syncPocSetting', pocSettingJson, lastModifyTime)
      },
      confirmEdit() {
        if (this.isNewStatus) {
          this.$refs.pocForm.validate(valid => {
            if (!valid) {
              return
            }
            // 同步poc setting数据到父组件
            this.syncPocSettingToDevice(JSON.stringify(this.pocSettingData), bfTime.nowUtcTime())
            this.pocVisible = false
          })
        } else {
          // 更新poc终端settings
          this.updatePocSetting()
        }
      },
      txGroupDmridChange(val) {
        if (val) {
          // 同步勾选上通讯录
          const target = bfglob.gorgData.getDataByIndex(val)
          this.pocTableTree.setNodeSelected(target.rid, true)
          // 默认发射组不在接收组中，则添加到接收组中
          if (!this.pocSettingData.rxGroupDmrids.includes(val)) {
            this.pocSettingData.rxGroupDmrids.push(val)
          }
        }
      },
      updatePocSetting() {
        this.$refs.pocForm.validate(valid => {
          if (valid) {
            const pocSettingObj = Object.assign({}, this.pocSettingData)
            if (pocSettingObj.password) {
              pocSettingObj.password = bfCrypto.sha256(this.device.dmrId + this.pocSettingData.password)
            } else {
              pocSettingObj.password = passwordCache
            }
            const pocSetting = JSON.stringify(pocSettingObj)

            // const msgObj = {
            //   ...this.device,
            //   pocSetting,
            //   pocSettingLastModifyTime: bfTime.nowUtcTime(),
            // }

            const msgObj = {
              orgId: this.device.orgId,
              rid: this.device.rid,
              dmrId: this.device.dmrId,
              pocSetting,
              pocSettingLastModifyTime: bfTime.nowUtcTime(),
            }

            const msgOpts = {
              rpcCmdFields: {
                origReqId: 'rid',
                resInfo: 'org_id,poc_setting,poc_setting_last_modify_time',
              },
            }

            return bfproto
              .sendMessage(dbCmd.DB_DEVICE_PUPDATE, msgObj, 'db_device', this.dbSubject, msgOpts)
              .then(rpc_cmd_obj => {
                bfglob.console.log('update poc setting res:', rpc_cmd_obj)
                const isOk = rpc_cmd_obj.resInfo === '+OK'
                if (isOk) {
                  bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
                  // 添加查询日志
                  const note = this.$t('dialog.update') + msgObj.selfId + ' / ' + msgObj.dmrId + this.$t('dialog.pocDevice') + this.$t('msgbox.deviceData')
                  bfglob.emit('addnote', note)
                  // 更新全局组织机构数据
                  bfglob.emit('update_global_deviceData', {
                    ...this.device,
                    ...msgObj,
                  })
                  // 同步到父组件pocDeviceData
                  this.syncPocSettingToDevice(msgObj.pocSetting, msgObj.pocSettingLastModifyTime)
                  this.pocVisible = false
                } else {
                  bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
                }
                return Promise.resolve(isOk)
              })
              .catch(err => {
                bfglob.console.warn('update device timeout:', err)
                bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
                return Promise.resolve(false)
              })
          }
        })
      },
      // 三个参数： 当前值、数据移动的方向（'left' / 'right'）、发生移动的数据 key 数组
      checkChange(_, direction, __) {
        if (direction === 'left' && this.pocSettingData.rxGroupDmrids.length === 0 && this.pocSettingData.txGroupDmrid) {
          this.pocSettingData.rxGroupDmrids.push(this.pocSettingData.txGroupDmrid)
        }
      },
    },
    mounted() {
      this.initPocSetting()
    },
  }
</script>

<style lang="scss">
  .el-dialog.poc-settings-dialog {
    width: 900px;
    //margin-top: 12vh !important;
    .el-form.poc-page-form {
      height: 500px;

      .poc-form-container {
        width: 480px;
      }

      .poc-tree-title {
        margin-left: 20px;
      }

      .el-form-item {
        margin-bottom: unset;

        label.el-form-item__label {
          padding: 0;
        }
      }

      .form-item-tree {
        .fancytree-grid-container {
          border: 1px solid #ebeef5;
          border-radius: 4px;
          height: calc(100% - 30px - 18px);

          table {
            table-layout: fixed;

            tbody tr {
              height: 22px !important;
            }
          }
        }
      }

      .form-item-transfer {
        .el-form-item__content .el-transfer {
          .el-transfer__buttons {
            display: flex;
            flex-direction: column;
            padding: 0;
            gap: 12px;

            .el-button {
              border-radius: 50%;
              padding: 12px;
              margin: 0;
              width: 40px;
              height: 40px;
            }
          }

          .el-transfer-panel {
            .el-transfer-panel__body {
              height: 284px;

              .el-transfer-panel__list {
                height: 100%;

                label.el-transfer-panel__item {
                  height: 24px;
                }
              }
            }
          }
        }
      }

      .mobile-transfer {
        .el-transfer {
          .el-transfer-panel {
            width: 100%;
          }

          .el-transfer__buttons {
            transform: rotate(90deg);
          }
        }
      }

      .el-form-item.password {
        .el-input .el-input__suffix .el-input__suffix-inner {
          display: flex;
        }
      }
    }
  }

  .el-message-box.poc-alert-message {
    .el-message-box__container {
      .el-message-box__status {
        top: 12px;
      }

      .el-message-box__status::before {
        font-size: 22px;
      }

      .el-message-box__message {
        padding-left: 28px;
      }
    }
  }
</style>
